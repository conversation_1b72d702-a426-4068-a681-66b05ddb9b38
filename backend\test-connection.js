// Simple test script to verify backend connection
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });

console.log('🔍 Testing Backend Configuration...\n');

console.log('📋 Environment Variables:');
console.log(`PORT: ${process.env.PORT}`);
console.log(`FRONTEND_URL: ${process.env.FRONTEND_URL}`);
console.log(`SMTP_HOST: ${process.env.SMTP_HOST}`);
console.log(`SMTP_PORT: ${process.env.SMTP_PORT}`);
console.log(`SMTP_USER: ${process.env.SMTP_USER}`);
console.log(`FROM_EMAIL: ${process.env.FROM_EMAIL}`);
console.log(`TO_EMAIL: ${process.env.TO_EMAIL}\n`);

// Test email service
console.log('📧 Testing Email Service...');
try {
  const emailService = require('./services/emailService');
  console.log('✅ Email service loaded successfully');
  
  // Test SMTP connection
  emailService.verifyConnection()
    .then(isConnected => {
      if (isConnected) {
        console.log('✅ SMTP connection successful!');
        console.log('\n🎉 Backend is ready to receive form submissions!');
        console.log(`🌐 Test URL: http://localhost:${process.env.PORT}/health`);
      } else {
        console.log('❌ SMTP connection failed');
        console.log('🔧 Check your SMTP credentials in .env file');
      }
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ SMTP test failed:', error.message);
      process.exit(1);
    });
    
} catch (error) {
  console.error('❌ Failed to load email service:', error.message);
  process.exit(1);
}
