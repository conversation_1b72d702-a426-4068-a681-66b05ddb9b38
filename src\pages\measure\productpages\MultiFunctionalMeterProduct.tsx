import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Check,
  Download,
  Mail,
  Phone,
  Zap,
  Monitor,
  Database,
  Wifi,
  Battery,
  Thermometer,
  ChevronDown,
  ChevronRight,
  Gauge,
  Shield,
  BarChart
} from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';
import Carousel from '@/components/Carousel';

// TypeScript interfaces for better type safety
interface ProductData {
  id: string;
  model: string;
  subtitle: string;
  image: string;
  images: string[];
  voltage: string;
  measurement: string;
  accuracy: string;
  price: string;
  description: string;
  keyFeatures: string[];
  technicalSpecs: Record<string, string>;
  applications: string[];
  advantages: string[];
}

interface ProductList {
  id: string;
  model: string;
  subtitle: string;
}

const PDF_URL = '/T&M April 2025.pdf';

const MultiFunctionalMeterProduct = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [featuresExpanded, setFeaturesExpanded] = useState(false);
  const [specsExpanded, setSpecsExpanded] = useState(false);

  // Product list for dropdown
  const productList: ProductList[] = [
    { id: 'digi530', model: 'DiGi 530S/H', subtitle: 'Standard Multi Functional Meter' },
    { id: 'digi630', model: 'DiGi 630S/D', subtitle: 'Advanced Multi Functional Meter' },
    { id: 'digi730', model: 'DiGi 730S/D', subtitle: 'Premium Multi Functional Meter' },
    { id: 'digi760', model: 'DiGi 760', subtitle: 'High Precision Meter' },
    { id: 'digi820', model: 'DiGi 820', subtitle: 'Class A Power Quality Analyzer' },
    { id: 'multy4', model: 'Multy4', subtitle: 'Multi-Channel Meter' },
    { id: 'plmr90', model: 'PLM R90', subtitle: 'DC Energy Meter' },
    { id: 'plmr91', model: 'PLM R91', subtitle: 'AC Energy Meter' },
    { id: 'plmr93', model: 'PLM R93', subtitle: '3-Phase Energy Meter' },
    { id: 'eon40', model: 'EON 4.0', subtitle: 'Advanced Energy Meter' }
  ];

  // Complete product data
  const productData: Record<string, ProductData> = {
    digi530: {
      id: 'digi530',
      model: 'DiGi 530S/H',
      subtitle: 'Standard Multi Functional Meter',
      image: '/Multifunctional meters/630-removebg-preview.png',
      images: [
        '/Multifunctional meters/630-removebg-preview.png'
      ],
      voltage: 'CAT III 480V',
      measurement: 'Energy & Power',
      accuracy: 'Class 0.5s',
      price: 'Contact for pricing',
      description: 'The DiGi 530S/H offers reliable energy and power measurement for industrial and panel applications, with advanced features and high accuracy for comprehensive monitoring solutions.',
      keyFeatures: [
        'Accuracy Class 0.5s as per IEC 62053-22',
        '3-line bright LCD display',
        'PT & CT programmable',
        'Password protection for setup page',
        'RS485 port with Modbus RTU protocol',
        'THD & Harmonics up to 31st order',
        'Load management feature',
        'Bi-directional energy measurement',
        'Compact 96mm x 96mm panel mount design',
        'Wide operating temperature range'
      ],
      technicalSpecs: {
        'RMS Values': 'V, U, I, In, Hz',
        'Power': 'kW, kVAr, kVA',
        'Energy': 'kWh, kVAh, kVArh',
        'THD': 'Voltage & Current',
        'Harmonics': 'Up to 31st order (530H)',
        'Communication': 'RS485 Modbus RTU',
        'Accuracy': 'Class 0.5s',
        'Display': '3-line LCD',
        'Dimensions': '96mm x 96mm x 83mm',
        'Cut-Out': '90 x 90 mm (+/- 0.5mm)',
        'Operating Temperature': '-10°C to +55°C',
        'Power Supply': '85-265V AC/DC',
        'Protection Rating': 'IP51 (front panel)',
        'Certification': 'IEC 62053-22 compliant'
      },
      applications: [
        'Industrial energy monitoring',
        'Panel metering',
        'Load management',
        'Energy billing',
        'Power quality monitoring',
        'Building automation'
      ],
      advantages: [
        'High accuracy measurements',
        'Compact design',
        'Easy installation',
        'Reliable performance',
        'Cost-effective solution',
        'User-friendly operation'
      ]
    },
    digi630: {
      id: 'digi630',
      model: 'DiGi 630S/D',
      subtitle: 'Advanced Multi Functional Meter',
      image: '/Multifunctional meters/630-removebg-preview.png',
      images: [
        '/Multifunctional meters/630-removebg-preview.png'
      ],
      voltage: 'CAT III 480V',
      measurement: 'Energy & Power',
      accuracy: 'Class 0.5s/0.2s',
      price: 'Contact for pricing',
      description: 'The DiGi 630S/D provides advanced energy measurement with digital I/O capabilities and enhanced communication features for comprehensive monitoring and control applications.',
      keyFeatures: [
        'Accuracy Class 0.5s / Class 0.2s as per IEC 62053-22',
        '3-line bright LCD display',
        'PT & CT programmable',
        '4 DI, 2 DO (DiGi 630D)',
        'Screen shot recording for energy accounting',
        'RS485 port with Modbus RTU protocol',
        'Modbus custom table',
        'Bi-directional energy measurement',
        'Advanced alarm and control functions',
        'Data logging capabilities'
      ],
      technicalSpecs: {
        'RMS Values': 'V, U, I, In, Hz',
        'Power': 'kW, kVAr, kVA, PF - Phase wise & Total',
        'Energy': 'kWh, kVAh, kVArh with bi-directional, 4 Quadrant capability',
        'THD': 'Voltage & Current',
        'Harmonics': 'Individual Harmonics up to 31st order',
        'Digital I/O': '4 DI, 2 DO (630D)',
        'Communication': 'RS485 Modbus RTU',
        'Accuracy': 'Class 0.5s/0.2s',
        'Display': '3-line LCD',
        'Dimensions': '96mm x 96mm x 83mm',
        'Operating Temperature': '-10°C to +55°C',
        'Power Supply': '85-265V AC/DC',
        'Memory': 'Data logging and event storage',
        'Alarm Functions': 'Configurable threshold alarms'
      },
      applications: [
        'Advanced energy monitoring',
        'Industrial automation',
        'Load control and management',
        'Energy billing and accounting',
        'Power quality analysis',
        'Building management systems'
      ],
      advantages: [
        'Dual accuracy classes',
        'Digital I/O capabilities',
        'Advanced communication',
        'Comprehensive measurements',
        'Flexible configuration',
        'Industrial-grade reliability'
      ]
    },
    digi730: {
      id: 'digi730',
      model: 'DiGi 730S/D',
      subtitle: 'Premium Multi Functional Meter',
      image: '/Multifunctional meters/730-removebg-preview.png',
      images: [
        '/Multifunctional meters/730-removebg-preview.png'
      ],
      voltage: 'CAT III 480V',
      measurement: 'Energy & Power',
      accuracy: 'Class 0.5s/0.2s',
      price: 'Contact for pricing',
      description: 'The DiGi 730S/D offers premium energy measurement with extended harmonics analysis and advanced I/O capabilities for demanding industrial and commercial applications.',
      keyFeatures: [
        'Accuracy Class 0.5s /class 0.2s as per IEC 62053-22',
        '3-line bright LCD display',
        'PT & CT programmable',
        '6 Digital inputs, 3DO & 1AO (730D)',
        'Tariff Energy Recording (6 rates)',
        'RS485 port with Modbus RTU protocol',
        'Baud rate up to 115,200 bps',
        'Individual Harmonics up to 51st order',
        'Advanced power quality analysis',
        'Comprehensive data logging'
      ],
      technicalSpecs: {
        'RMS Values': 'V, U, I, In, Hz',
        'Power': 'kW, kVAr, kVA, PF - Phase wise & Total',
        'Energy': 'kWh, kVAh, kVArh with bi-directional, 4-Quadrant capability',
        'THD': 'Voltage & Current',
        'Harmonics': 'Individual Harmonics up to 51st order',
        'Digital I/O': '6 DI, 3 DO, 1 AO (730D)',
        'Communication': 'RS485 Modbus RTU up to 115,200 bps',
        'Accuracy': 'Class 0.5s/0.2s',
        'Tariff': '6 rates',
        'Dimensions': '96mm x 96mm x 83mm',
        'Operating Temperature': '-10°C to +55°C',
        'Power Supply': '85-265V AC/DC',
        'Data Storage': 'Extended memory for historical data',
        'Communication Speed': 'High-speed data transfer'
      },
      applications: [
        'Premium energy monitoring',
        'Industrial process control',
        'Multi-tariff energy billing',
        'Advanced power quality analysis',
        'Building automation systems',
        'Renewable energy monitoring'
      ],
      advantages: [
        'Extended harmonics analysis',
        'Multiple tariff rates',
        'High-speed communication',
        'Advanced I/O capabilities',
        'Comprehensive measurements',
        'Professional-grade performance'
      ]
    },
    digi760: {
      id: 'digi760',
      model: 'DiGi 760',
      subtitle: 'High Precision Meter',
      image: '/Multifunctional meters/760-removebg-preview.png',
      images: [
        '/Multifunctional meters/760-removebg-preview.png'
      ],
      voltage: 'CAT III 480V',
      measurement: 'Energy & Power Quality',
      accuracy: 'Class 0.2s',
      price: 'Contact for pricing',
      description: 'The DiGi 760 features a TFT color display with real-time waveform visualization and advanced power quality monitoring capabilities for critical applications.',
      keyFeatures: [
        'Accuracy Class 0.2s as per IEC 62053-22',
        'TFT colorful LCD display with 320 x 240 resolution',
        'Real Time waveform Display',
        'Voltage swell/sag event recording with waveform',
        'Ethernet Port with Modbus TCP/IP protocol',
        'Individual harmonics on V & I up to 63rd order',
        'K-Factor, Crest Factor measurements',
        '4AO, 8DI (Optional)',
        'Advanced power quality analysis',
        'Event recording with timestamps'
      ],
      technicalSpecs: {
        'RMS Values': 'V, U, I, In, Hz',
        'Power': 'kW, kVA, kVAr – phase wise and total',
        'Energy': 'kWh, kVAh, kVArh with bi-directional measurements',
        'THD': 'Voltage & Current',
        'Harmonics': 'Individual harmonics on V & I up to 63rd order',
        'Display': 'TFT colorful LCD 320 x 240',
        'Communication': 'Ethernet Modbus TCP/IP, RS485',
        'Accuracy': 'Class 0.2s',
        'I/O': '4AO, 8DI (Optional)',
        'Dimensions': '96 x 96 x 66mm',
        'Waveform Capture': 'Real-time waveform display',
        'Event Storage': 'Comprehensive event logging',
        'K-Factor': 'Transformer derating factor calculation',
        'Crest Factor': 'Current and voltage crest factor'
      },
      applications: [
        'High-precision energy monitoring',
        'Power quality analysis',
        'Industrial automation',
        'Critical facility monitoring',
        'Data center monitoring',
        'Laboratory applications'
      ],
      advantages: [
        'Color TFT display',
        'Real-time waveforms',
        'High precision measurements',
        'Ethernet connectivity',
        'Advanced power quality features',
        'Professional visualization'
      ]
    },
    digi820: {
      id: 'digi820',
      model: 'DiGi 820',
      subtitle: 'Class A Power Quality Analyzer',
      image: '/Multifunctional meters/digi820-removebg-preview.png',
      images: [
        '/Multifunctional meters/digi820-removebg-preview.png'
      ],
      voltage: 'CAT III 480V',
      measurement: 'Power Quality & Energy',
      accuracy: 'Class A/0.2s',
      price: 'Contact for pricing',
      description: 'The DiGi 820 is an IEC 61000-4-30 Class A power quality monitoring meter with advanced analysis capabilities and extensive data storage for professional applications.',
      keyFeatures: [
        'IEC 61000-4-30 Class A power quality monitoring meter',
        'Class 0.2s high accuracy energy measurement',
        'Sampling rate: 1024 samples/cycle',
        'Color LCD Display with resolution of 640 x 480',
        '8GB memory for data and event recording',
        'Records 256 PQ events along with waveform',
        'Captures 20μs Voltage transients',
        'Harmonics & Inter-harmonics up to 63rd order',
        'Advanced statistical analysis',
        'Comprehensive reporting capabilities'
      ],
      technicalSpecs: {
        'RMS Values': 'V, U, I, In, Hz',
        'Power': 'kW, kVAr, kVA & PF: Phase & Total Power',
        'Energy': '4-Quadrant energy measurements',
        'Harmonics': 'Harmonics & Inter-harmonics up to 63rd order',
        'PQ Events': 'Records 256 PQ events along with waveform',
        'Display': 'Color LCD 640 x 480',
        'Memory': '8GB for data and event recording',
        'Sampling': '1024 samples/cycle',
        'Communication': 'Ethernet Modbus TCP/IP, RS485',
        'Standard': 'IEC 61000-4-30 Class A',
        'Transient Capture': '20μs voltage transients',
        'Statistical Analysis': 'Min, max, average calculations',
        'Data Export': 'Multiple file formats supported',
        'Web Interface': 'Built-in web server for remote access'
      },
      applications: [
        'Power quality monitoring',
        'Utility grid analysis',
        'Industrial power systems',
        'Critical infrastructure monitoring',
        'Renewable energy integration',
        'Compliance monitoring'
      ],
      advantages: [
        'Class A power quality standard',
        'High sampling rate',
        'Extensive data storage',
        'Advanced event recording',
        'Professional analysis tools',
        'Comprehensive monitoring'
      ]
    },
    multy4: {
      id: 'multy4',
      model: 'Multy4',
      subtitle: 'Multi-Channel Meter',
      image: '/Multifunctional meters/Multy_4-removebg-preview.png',
      images: [
        '/Multifunctional meters/Multy_4-removebg-preview.png'
      ],
      voltage: 'CAT III 500V',
      measurement: '4 Channel Monitoring',
      accuracy: 'Class 1.0',
      price: 'Contact for pricing',
      description: 'The Multy4 provides multi-channel energy monitoring with special high-accuracy CT for comprehensive monitoring of multiple circuits in a single compact unit.',
      keyFeatures: [
        'Backlit custom LCD display',
        'Accuracy Class 1.0 as per IEC 62053-21',
        'CT primary programmable with 2kV isolation',
        'Special high-accuracy CT supplied',
        'RS485-modbus RTU Protocol',
        'Load management module',
        'Password protection',
        'Compact design',
        'Multi-circuit capability',
        'Cost-effective monitoring solution'
      ],
      technicalSpecs: {
        'Configuration': 'Single phase/Three phase: 12 x 1 phase / 4 x 3 phase',
        'Voltage': 'Up to 500V',
        'Current': 'Line current for each channel',
        'Power': 'kW, kVAr, kVA & PF: Phase and Total for each channel',
        'Energy': 'kWh, kVAh: Phase and Total for each channel',
        'Communication': 'RS485-modbus RTU Protocol',
        'Accuracy': 'Class 1.0',
        'Display': 'Backlit custom LCD',
        'Dimensions': '75mm x 94mm x 62mm',
        'CT Specifications': 'High-accuracy current transformers included',
        'Isolation': '2kV between channels',
        'Load Management': 'Built-in load control functions',
        'Data Storage': 'Historical data logging'
      },
      applications: [
        'Multi-circuit monitoring',
        'Industrial energy management',
        'Building automation',
        'Load distribution monitoring',
        'Energy billing',
        'Power management systems'
      ],
      advantages: [
        'Multi-channel capability',
        'High-accuracy CT included',
        'Flexible configuration',
        'Compact installation',
        'Cost-effective monitoring',
        'Reliable performance'
      ]
    },
    plmr90: {
      id: 'plmr90',
      model: 'PLM R90',
      subtitle: 'DC Energy Meter',
      image: '/Multifunctional meters/PLm_R90-removebg-preview.png',
      images: [
        '/Multifunctional meters/PLm_R90-removebg-preview.png'
      ],
      voltage: 'DC Applications',
      measurement: '1Φ DC Energy',
      accuracy: 'Class 0.5',
      price: 'Contact for pricing',
      description: 'The PLM R90 is designed for DC energy measurement with DIN rail mounting for easy installation in DC power systems and renewable energy applications.',
      keyFeatures: [
        '35mm DIN rail installing',
        'Accuracy: Class 0.5',
        'LED indicates pulse output',
        'RS485 port (Optional)',
        '2 module size',
        'Shunt options: 100A, 200A, 300A',
        'Compact design',
        'Easy installation',
        'DC measurement capability',
        'Solar application ready'
      ],
      technicalSpecs: {
        'Type': '1Φ DC Energy Meter',
        'RMS Values': 'V, I',
        'Power': 'kW',
        'Energy': 'kWh',
        'Shunt Options': '100A, 200A, 300A',
        'Communication': 'RS485 (Optional)',
        'Accuracy': 'Class 0.5',
        'Installation': 'DIN Rail',
        'Dimensions': '100 x 36 x 65mm',
        'Operating Temperature': '-25°C to +55°C',
        'Power Supply': 'DC voltage measurement range',
        'Pulse Output': 'LED indication and pulse output',
        'Protection': 'Reverse polarity protection'
      },
      applications: [
        'DC power systems',
        'Solar energy monitoring',
        'Battery monitoring',
        'DC motor applications',
        'Renewable energy systems',
        'Industrial DC applications'
      ],
      advantages: [
        'DC measurement capability',
        'DIN rail mounting',
        'High accuracy',
        'Compact size',
        'Multiple shunt options',
        'Cost-effective solution'
      ]
    },
    plmr91: {
      id: 'plmr91',
      model: 'PLM R91',
      subtitle: 'AC Energy Meter',
      image: '/Multifunctional meters/R_91-removebg-preview.png',
      images: [
        '/Multifunctional meters/R_91-removebg-preview.png'
      ],
      voltage: 'Single Phase AC',
      measurement: '1Φ AC Energy',
      accuracy: 'Class 1.0',
      price: 'Contact for pricing',
      description: 'The PLM R91 provides single-phase AC energy measurement with DIN rail mounting for residential and light commercial applications with reliable performance.',
      keyFeatures: [
        '35mm DIN rail installation',
        'Active energy accuracy Class 1.0',
        '6+1 digit LCD display',
        'Passive pulse output',
        'RS485 port (optional)',
        'LED pulse indicator',
        'Max current 63A',
        'Compact design',
        'Easy installation',
        'Cost-effective solution'
      ],
      technicalSpecs: {
        'Type': '1Φ AC Energy Meter',
        'RMS Values': 'V, I, Hz',
        'Power': 'kW, kVAr, kVA, PF',
        'Energy': 'kWh, kVArh',
        'Max Current': '63A',
        'Display': '6+1 digit LCD',
        'Communication': 'RS485 (Optional)',
        'Accuracy': 'Class 1.0',
        'Dimensions': '100 x 36 x 65mm',
        'Operating Temperature': '-25°C to +55°C',
        'Power Supply': 'Direct connection',
        'Pulse Output': 'Passive pulse output',
        'Installation': 'DIN rail mounting'
      },
      applications: [
        'Residential energy monitoring',
        'Small commercial applications',
        'Sub-metering',
        'Energy billing',
        'Load monitoring',
        'Building management'
      ],
      advantages: [
        'Single-phase monitoring',
        'Easy DIN rail installation',
        'LCD display',
        'Pulse output',
        'Optional communication',
        'Cost-effective'
      ]
    },
    plmr93: {
      id: 'plmr93',
      model: 'PLM R93',
      subtitle: '3-Phase Energy Meter',
      image: '/Multifunctional meters/R_93-removebg-preview.png',
      images: [
        '/Multifunctional meters/R_93-removebg-preview.png'
      ],
      voltage: 'Three Phase AC',
      measurement: '3Φ AC Energy',
      accuracy: 'Class 1.0',
      price: 'Contact for pricing',
      description: 'The PLM R93 offers comprehensive three-phase energy measurement with advanced features and historical data recording for industrial and commercial applications.',
      keyFeatures: [
        '35mm DIN rail installation',
        'kWh Class 1.0 as per IEC 62053-21',
        '7+1 digit LCD display',
        'Phase sequence error indication',
        'RS485 port with Modbus-RTU protocol',
        '2 LED Pulse indicators',
        'Multi-tariff capability',
        'Historical data storage',
        'Bidirectional measurement',
        'Comprehensive three-phase monitoring'
      ],
      technicalSpecs: {
        'Type': '3Φ AC Energy Meter',
        'RMS Values': 'V, I, Hz',
        'Power': 'kW, kVAr, kVA, PF',
        'Energy': 'kWh, kVArh with bidirectional measurement',
        'Max Current': '63A',
        'Display': '7+1 digit LCD',
        'Communication': 'RS485 Modbus-RTU',
        'Accuracy': 'Class 1.0',
        'Dimensions': '72 x 100 x 65mm',
        'Operating Temperature': '-25°C to +55°C',
        'Phase Sequence': 'Error detection and indication',
        'Tariff Capability': 'Multi-tariff energy recording',
        'Data Storage': 'Historical energy data'
      },
      applications: [
        'Three-phase energy monitoring',
        'Industrial applications',
        'Commercial buildings',
        'Multi-tariff billing',
        'Load management',
        'Energy analysis'
      ],
      advantages: [
        'Three-phase capability',
        'Bidirectional measurement',
        'Multi-tariff support',
        'Historical data',
        'Phase sequence detection',
        'Comprehensive monitoring'
      ]
    },
    eon40: {
      id: 'eon40',
      model: 'EON 4.0',
      subtitle: 'Advanced Energy Meter',
      image: '/assets/meters/eon40.png',
      images: [
        '/assets/meters/eon40.png'
      ],
      voltage: 'CAT III 480V',
      measurement: 'IoT Energy Monitoring',
      accuracy: 'Class 0.5',
      price: 'Contact for pricing',
      description: 'The EON 4.0 series provides advanced energy measurement with IoT connectivity options for modern energy management systems and smart grid applications.',
      keyFeatures: [
        'Active energy accuracy up to Class 0.5',
        'PT/CT programmable',
        'Auto Phase sequence adjustment',
        '2 DI Standard, 2 DO (optional)',
        'Multiple communication options',
        'Gateway function (4.0G & 4.0E)',
        'Built-in memory for data storage',
        'Remote configuration capability',
        'IoT connectivity',
        'Cloud integration ready'
      ],
      technicalSpecs: {
        'RMS Values': 'V, I, Hz, V & I phase angle',
        'Power': 'kW, kVAR, kVA & PF',
        'Energy': 'kWh, kVArh, kVAh with bidirectional capability',
        'Harmonics': 'Individual Harmonics up to 63rd order',
        'Communication': 'RS485/4G/Ethernet options',
        'Accuracy': 'Class 0.5',
        'Memory': '7 days data storage (4.0G & 4.0E)',
        'I/O': '2 DI, 2 DO (optional)',
        'Battery Backup': '10 secs for power interruption events',
        'Gateway Function': 'Multi-meter data aggregation',
        'Remote Access': 'Cloud-based monitoring',
        'Auto Configuration': 'Phase sequence adjustment',
        'Data Logging': 'Comprehensive historical data'
      },
      applications: [
        'IoT energy monitoring',
        'Smart grid applications',
        'Remote monitoring',
        'Building automation',
        'Industrial IoT',
        'Energy management systems'
      ],
      advantages: [
        'IoT connectivity',
        'Multiple communication options',
        'Remote configuration',
        'Data storage capability',
        'Gateway functionality',
        'Modern energy management'
      ]
    }
  };

  const product = productData[productId as keyof typeof productData];

  useEffect(() => {
    if (!product) {
      navigate('/measure/multi-functional-meters');
    } else {
      document.title = `${product.model} - ${product.subtitle} | Multi Functional Meter`;
    }
  }, [product, navigate]);

  // Handle clicking outside dropdown to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (dropdownOpen && !target.closest('.dropdown-container')) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dropdownOpen]);

  if (!product) {
    return <div>Product not found</div>;
  }

  // Feature icon logic
  const FeatureIcon = ({ feature }: { feature: string }) => {
    if (feature.toLowerCase().includes('display') || feature.toLowerCase().includes('lcd') || feature.toLowerCase().includes('tft') || feature.toLowerCase().includes('screen')) return <Monitor className="h-5 w-5" />;
    if (feature.toLowerCase().includes('memory') || feature.toLowerCase().includes('storage') || feature.toLowerCase().includes('data') || feature.toLowerCase().includes('recording')) return <Database className="h-5 w-5" />;
    if (feature.toLowerCase().includes('communication') || feature.toLowerCase().includes('rs485') || feature.toLowerCase().includes('ethernet') || feature.toLowerCase().includes('modbus')) return <Wifi className="h-5 w-5" />;
    if (feature.toLowerCase().includes('power') || feature.toLowerCase().includes('battery') || feature.toLowerCase().includes('supply')) return <Battery className="h-5 w-5" />;
    if (feature.toLowerCase().includes('temperature') || feature.toLowerCase().includes('thermal')) return <Thermometer className="h-5 w-5" />;
    if (feature.toLowerCase().includes('voltage') || feature.toLowerCase().includes('current') || feature.toLowerCase().includes('energy') || feature.toLowerCase().includes('bi-directional')) return <Zap className="h-5 w-5" />;
    if (feature.toLowerCase().includes('measurement') || feature.toLowerCase().includes('accuracy') || feature.toLowerCase().includes('harmonics') || feature.toLowerCase().includes('tariff')) return <Gauge className="h-5 w-5" />;
    if (feature.toLowerCase().includes('protection') || feature.toLowerCase().includes('password') || feature.toLowerCase().includes('isolation')) return <Shield className="h-5 w-5" />;
    if (feature.toLowerCase().includes('analysis') || feature.toLowerCase().includes('quality') || feature.toLowerCase().includes('monitoring') || feature.toLowerCase().includes('waveform')) return <BarChart className="h-5 w-5" />;
    return <Check className="h-5 w-5" />;
  };

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      {/* Hide Breadcrumbs and Remove Top Padding */}
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>

      <div className="min-h-screen bg-yellow-50" style={{ fontFamily: 'Open Sans, sans-serif' }}>
        {/* Main Title Section */}
        <div className="py-8" style={{ background: '#F5C842' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            
            {/* Title always at top in mobile, center in desktop */}
            <div className="text-center mb-4 md:mb-0">
              <h1 className="text-4xl md:text-5xl font-bold text-black mb-2">
                Multi Functional Meters
              </h1>
              <p className="text-xl text-black font-medium">
                Advanced Energy Measurement Solutions
              </p>
            </div>
            {/* Responsive flex container for dropdown and back button */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between w-full gap-4 md:gap-0">
              {/* Dropdown first on mobile, right on desktop */}
              <div className="order-1 md:order-2 w-full md:w-auto flex justify-center md:block dropdown-container">
                <div className="relative w-full md:w-auto group">
                  <button
                    onClick={() => setDropdownOpen(!dropdownOpen)}
                    className="bg-white border border-yellow-400 text-black font-bold py-3 px-6 rounded-xl shadow-md flex items-center space-x-2 w-full md:w-auto justify-center md:justify-start transition-colors duration-200 focus:outline-none hover:bg-yellow-50"
                    style={{ fontWeight: 700, fontSize: '1.25rem' }}
                  >
                    <span>{product.model}</span>
                    <ChevronDown className={`h-4 w-4 ml-2 transition-transform duration-200 ${dropdownOpen ? 'rotate-180' : ''}`} />
                  </button>
                  {dropdownOpen && (
                    <div className="absolute top-full left-0 right-0 md:right-auto md:w-80 mt-2 bg-white border border-yellow-400 rounded-xl shadow-lg z-50 max-h-80 overflow-y-auto">
                      {productList.map((prod) => (
                        <button
                          key={prod.id}
                          onClick={() => {
                            setDropdownOpen(false);
                            navigate(`/measure/multi-functional-meters/product/${prod.id}`);
                          }}
                          className={`w-full text-left px-4 py-3 hover:bg-yellow-50 transition-colors duration-200 border-b border-gray-100 last:border-b-0 ${
                            prod.id === product.id ? 'bg-yellow-50 font-bold' : ''
                          }`}
                        >
                          <div className="font-bold text-black">{prod.model}</div>
                          <div className="text-sm text-gray-600">{prod.subtitle}</div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              {/* Back button second on mobile, left on desktop */}
              <div className="order-2 md:order-1 w-full md:w-auto flex justify-center md:justify-start">
                <button
                  onClick={() => navigate('/measure/multi-functional-meters')}
                  className="bg-white border border-yellow-400 text-black font-bold py-2 px-4 rounded-xl shadow-md hover:bg-yellow-50 transition-all duration-200 flex items-center space-x-2 w-full md:w-auto justify-center text-center"
                >
                  <span>&larr;</span>
                  <span>Back to Products</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Product Hero Section */}
        <div className="bg-gradient-to-br from-yellow-100 to-yellow-50 py-8 md:py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row justify-center md:justify-between items-center md:items-stretch gap-6 md:gap-8">
              {/* Content Left (on desktop) */}
              <div className="w-full md:w-1/2 max-w-2xl order-2 md:order-1 flex flex-col justify-center">
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  className="space-y-4 order-1 lg:order-1"
                >
                  <div className="inline-block px-3 py-1.5 rounded-full text-black font-bold text-xs mb-3" style={{ backgroundColor: '#F5C842' }}>
                    {product.measurement}
                  </div>
                  <h1 className="text-3xl md:text-4xl font-bold text-black mb-3">
                    {product.model}
                  </h1>
                  <p className="text-lg text-yellow-700 font-semibold mb-4">
                    {product.subtitle}
                  </p>
                  <p className="text-base text-black leading-relaxed mb-6">
                    {product.description}
                  </p>
                  {/* Quick Specs */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Safety Rating</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.voltage}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Accuracy</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.accuracy}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md col-span-2">
                      <h4 className="font-semibold text-black mb-1">Price</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.price}</p>
                    </div>
                  </div>
                  {/* Action Buttons at Bottom */}
                  <div className="flex flex-col sm:flex-row gap-3 pt-4">
                    <button onClick={() => navigate('/contact/sales')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Phone className="h-5 w-5" />
                      <span>Request Demo</span>
                    </button>
                    <button onClick={() => window.open(PDF_URL, '_blank')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Download className="h-5 w-5" />
                      <span>View Brochure</span>
                    </button>
                  </div>
                </motion.div>
              </div>
              {/* Image Carousel Right (on desktop) */}
              <div className="w-full md:w-1/2 flex items-center justify-center order-1 md:order-2 mb-6 md:mb-0">
                <div className="w-full max-w-xs">
                  {product.images && product.images.length > 1 ? (
                    <Carousel
                      images={product.images}
                      className="w-full"
                      theme="yellow"
                    />
                  ) : (
                    <img
                      src={product.image}
                      alt={product.model}
                      className="w-full h-auto object-contain"
                      style={{ 
                        maxHeight: '200px',
                        maxWidth: '200px',
                        background: 'transparent',
                        mixBlendMode: 'multiply',
                        filter: 'brightness(1.1) contrast(1.1)',
                        opacity: '0.95'
                      }}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Features and Technical Specifications Section */}
        <div className="py-8 md:py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-12 gap-4 md:gap-6">
              <div className="md:col-span-5">
                {/* Key Features Section - Expandable Card */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="w-full bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col h-full"
                >
                  {/* Header */}
                  <div className="p-6">
                    <h2 className="text-2xl font-bold text-gray-900">Key Features</h2>
                  </div>
                  
                  {/* Content Area - Flex Grow */}
                  <div className="flex-1 flex flex-col">
                    {/* Preview Content - Always Visible */}
                    <div className="px-6 pb-6 space-y-4 flex-1">
                      {product.keyFeatures.slice(0, 6).map((feature, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.4, delay: index * 0.05 }}
                          className="flex items-start gap-4 p-3 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                        >
                          <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <FeatureIcon feature={feature} />
                          </div>
                          <span className="text-gray-800 font-medium leading-relaxed">{feature}</span>
                        </motion.div>
                      ))}
                    </div>
                    
                    {/* Expandable Content - Additional Features */}
                    {product.keyFeatures.length > 6 && (
                      <AnimatePresence>
                        {featuresExpanded && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: "auto", opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                            className="overflow-hidden"
                          >
                            <div className="px-6 pb-6 space-y-4 border-t border-gray-100 pt-4">
                              {product.keyFeatures.slice(6).map((feature, index) => (
                                <motion.div
                                  key={index + 6}
                                  initial={{ opacity: 0, x: -20 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ duration: 0.4, delay: index * 0.05 }}
                                  className="flex items-start gap-4 p-3 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                                >
                                  <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <FeatureIcon feature={feature} />
                                  </div>
                                  <span className="text-gray-800 font-medium leading-relaxed">{feature}</span>
                                </motion.div>
                              ))}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    )}
                  </div>
                  
                  {/* Show More/Less Button - Always at Bottom */}
                  {product.keyFeatures.length > 6 && (
                    <div className="px-6 pb-6 border-t border-gray-100 pt-4 mt-auto">
                      <button
                        onClick={() => setFeaturesExpanded(!featuresExpanded)}
                        className="w-full py-3 px-4 text-black hover:text-black font-semibold text-sm flex items-center justify-center gap-2 transition-all duration-200 rounded-lg hover:bg-yellow-50 border border-yellow-200 hover:border-yellow-300"
                      >
                        {featuresExpanded ? (
                          <>
                            <span>Show Less</span>
                            <ChevronDown className="h-4 w-4" />
                          </>
                        ) : (
                          <>
                            <span>Show {product.keyFeatures.length - 6} More Features</span>
                            <ChevronRight className="h-4 w-4" />
                          </>
                        )}
                      </button>
                    </div>
                  )}
                </motion.div>
              </div>

              {/* Technical Specifications Section - Expandable Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="md:col-span-7 bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col h-full"
              >
                {/* Header */}
                <div className="p-6">
                  <h2 className="text-2xl font-bold text-gray-900">Technical Specifications</h2>
                </div>
                
                {/* Content Area - Flex Grow */}
                <div className="flex-1 flex flex-col">
                  {/* Preview Content - Always Visible */}
                  <div className="px-6 pb-6 flex-1">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <tbody>
                          {Object.entries(product.technicalSpecs).slice(0, 6).map(([key, value], index) => (
                            <motion.tr
                              key={key}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ duration: 0.4, delay: index * 0.05 }}
                              className={`border-b border-gray-100 hover:bg-yellow-50 transition-colors duration-200 ${
                                index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                              }`}
                            >
                              <td className="py-4 px-4 font-semibold text-gray-900 align-top w-1/3">
                                {key}
                              </td>
                              <td className="py-4 px-4 text-gray-700 font-medium">
                                {value}
                              </td>
                            </motion.tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                  
                  {/* Expandable Content - Additional Specifications */}
                  {Object.entries(product.technicalSpecs).length > 6 && (
                    <AnimatePresence>
                      {specsExpanded && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className="overflow-hidden"
                        >
                          <div className="px-6 pb-6 border-t border-gray-100 pt-4">
                            <div className="overflow-x-auto">
                              <table className="w-full">
                                <tbody>
                                  {Object.entries(product.technicalSpecs).slice(6).map(([key, value], index) => (
                                    <motion.tr
                                      key={key}
                                      initial={{ opacity: 0, x: -20 }}
                                      animate={{ opacity: 1, x: 0 }}
                                      transition={{ duration: 0.4, delay: index * 0.05 }}
                                      className={`border-b border-gray-100 hover:bg-yellow-50 transition-colors duration-200 ${
                                        index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                                      }`}
                                    >
                                      <td className="py-4 px-4 font-semibold text-gray-900 align-top w-1/3">
                                        {key}
                                      </td>
                                      <td className="py-4 px-4 text-gray-700 font-medium">
                                        {value}
                                      </td>
                                    </motion.tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  )}
                </div>
                
                {/* Show More/Less Button - Always at Bottom */}
                {Object.entries(product.technicalSpecs).length > 6 && (
                  <div className="px-6 pb-6 border-t border-gray-100 pt-4 mt-auto">
                    <button
                      onClick={() => setSpecsExpanded(!specsExpanded)}
                      className="w-full py-3 px-4 text-black hover:text-black font-semibold text-sm flex items-center justify-center gap-2 transition-all duration-200 rounded-lg hover:bg-yellow-50 border border-yellow-200 hover:border-yellow-300"
                    >
                      {specsExpanded ? (
                        <>
                          <span>Show Less</span>
                          <ChevronDown className="h-4 w-4" />
                        </>
                      ) : (
                        <>
                          <span>Show {Object.entries(product.technicalSpecs).length - 6} More Specifications</span>
                          <ChevronRight className="h-4 w-4" />
                        </>
                      )}
                    </button>
                  </div>
                )}
              </motion.div>
            </div>
          </div>
        </div>

        {/* Applications and Advantages Section */}
        <div className="py-8 md:py-12 bg-gradient-to-br from-yellow-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Applications */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="bg-white rounded-2xl shadow-lg p-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Applications</h2>
                <div className="space-y-3">
                  {product.applications.map((application, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                    >
                      <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                      <span className="text-gray-700 font-medium">{application}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Advantages */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white rounded-2xl shadow-lg p-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Key Advantages</h2>
                <div className="space-y-3">
                  {product.advantages.map((advantage, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                    >
                      <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                      <span className="text-gray-700 font-medium">{advantage}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="py-12 md:py-16 mb-16 md:mb-24 bg-gradient-to-br from-yellow-50 to-yellow-100 border-t-2 border-yellow-200">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Need Expert Advice?
              </h2>
              <p className="text-base md:text-lg text-gray-700 mb-10 font-medium">
                Our specialists provide comprehensive guidance on multi functional meter solutions
              </p>
              <button
                className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base mx-auto"
                onClick={() => navigate('/contact/sales')}
              >
                <Mail className="h-5 w-5" />
                <span>Contact Sales</span>
              </button>
            </motion.div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default MultiFunctionalMeterProduct;