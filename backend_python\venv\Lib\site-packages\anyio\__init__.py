from __future__ import annotations

__all__ = (
    "maybe_async",
    "maybe_async_cm",
    "run",
    "sleep",
    "sleep_forever",
    "sleep_until",
    "current_time",
    "get_all_backends",
    "get_cancelled_exc_class",
    "BrokenResourceError",
    "BrokenWorkerProcess",
    "BusyResourceError",
    "ClosedResourceError",
    "DelimiterNotFound",
    "EndOfStream",
    "ExceptionGroup",
    "IncompleteRead",
    "TypedAttributeLookupError",
    "WouldBlock",
    "AsyncFile",
    "Path",
    "open_file",
    "wrap_file",
    "aclose_forcefully",
    "open_signal_receiver",
    "connect_tcp",
    "connect_unix",
    "create_tcp_listener",
    "create_unix_listener",
    "create_udp_socket",
    "create_connected_udp_socket",
    "getaddrinfo",
    "getnameinfo",
    "wait_socket_readable",
    "wait_socket_writable",
    "create_memory_object_stream",
    "run_process",
    "open_process",
    "create_lock",
    "CapacityLimiter",
    "CapacityLimiterStatistics",
    "Condition",
    "ConditionStatistics",
    "Event",
    "EventStatistics",
    "Lock",
    "LockStatistics",
    "Semaphore",
    "SemaphoreStatistics",
    "create_condition",
    "create_event",
    "create_semaphore",
    "create_capacity_limiter",
    "open_cancel_scope",
    "fail_after",
    "move_on_after",
    "current_effective_deadline",
    "TASK_STATUS_IGNORED",
    "CancelScope",
    "create_task_group",
    "TaskInfo",
    "get_current_task",
    "get_running_tasks",
    "wait_all_tasks_blocked",
    "run_sync_in_worker_thread",
    "run_async_from_thread",
    "run_sync_from_thread",
    "current_default_worker_thread_limiter",
    "create_blocking_portal",
    "start_blocking_portal",
    "typed_attribute",
    "TypedAttributeSet",
    "TypedAttributeProvider",
)

from typing import Any

from ._core._compat import maybe_async, maybe_async_cm
from ._core._eventloop import (
    current_time,
    get_all_backends,
    get_cancelled_exc_class,
    run,
    sleep,
    sleep_forever,
    sleep_until,
)
from ._core._exceptions import (
    BrokenResourceError,
    BrokenWorkerProcess,
    BusyResourceError,
    ClosedResourceError,
    DelimiterNotFound,
    EndOfStream,
    ExceptionGroup,
    IncompleteRead,
    TypedAttributeLookupError,
    WouldBlock,
)
from ._core._fileio import AsyncFile, Path, open_file, wrap_file
from ._core._resources import aclose_forcefully
from ._core._signals import open_signal_receiver
from ._core._sockets import (
    connect_tcp,
    connect_unix,
    create_connected_udp_socket,
    create_tcp_listener,
    create_udp_socket,
    create_unix_listener,
    getaddrinfo,
    getnameinfo,
    wait_socket_readable,
    wait_socket_writable,
)
from ._core._streams import create_memory_object_stream
from ._core._subprocesses import open_process, run_process
from ._core._synchronization import (
    CapacityLimiter,
    CapacityLimiterStatistics,
    Condition,
    ConditionStatistics,
    Event,
    EventStatistics,
    Lock,
    LockStatistics,
    Semaphore,
    SemaphoreStatistics,
    create_capacity_limiter,
    create_condition,
    create_event,
    create_lock,
    create_semaphore,
)
from ._core._tasks import (
    TASK_STATUS_IGNORED,
    CancelScope,
    create_task_group,
    current_effective_deadline,
    fail_after,
    move_on_after,
    open_cancel_scope,
)
from ._core._testing import (
    TaskInfo,
    get_current_task,
    get_running_tasks,
    wait_all_tasks_blocked,
)
from ._core._typedattr import TypedAttributeProvider, TypedAttributeSet, typed_attribute

# Re-exported here, for backwards compatibility
# isort: off
from .to_thread import current_default_worker_thread_limiter, run_sync_in_worker_thread
from .from_thread import (
    create_blocking_portal,
    run_async_from_thread,
    run_sync_from_thread,
    start_blocking_portal,
)

# Re-export imports so they look like they live directly in this package
key: str
value: Any
for key, value in list(locals().items()):
    if getattr(value, "__module__", "").startswith("anyio."):
        value.__module__ = __name__
