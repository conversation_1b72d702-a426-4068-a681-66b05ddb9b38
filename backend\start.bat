@echo off
echo Starting Sales Form Backend Server...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if package.json exists
if not exist package.json (
    echo Error: package.json not found
    echo Please run this script from the backend directory
    pause
    exit /b 1
)

REM Check if node_modules exists, if not install dependencies
if not exist node_modules (
    echo Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Check if .env file exists
if not exist .env (
    echo Warning: .env file not found
    echo Please copy .env.example to .env and configure your SMTP settings
    echo.
    if exist .env.example (
        copy .env.example .env
        echo Created .env file from .env.example
        echo Please edit .env file with your SMTP credentials before running the server
        pause
    )
)

echo Starting server in development mode...
echo Server will be available at http://localhost:5000
echo Press Ctrl+C to stop the server
echo.

npm run dev
