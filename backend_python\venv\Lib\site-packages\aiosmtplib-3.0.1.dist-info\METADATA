Metadata-Version: 2.1
Name: aiosmtplib
Version: 3.0.1
Summary: asyncio SMTP client
Home-page: https://github.com/cole/aiosmtplib
License: MIT
Keywords: smtp,email,asyncio
Author: <PERSON>
Author-email: <EMAIL>
Requires-Python: >=3.8,<4.0
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: No Input/Output (Daemon)
Classifier: Framework :: AsyncIO
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Communications
Classifier: Topic :: Communications :: Email
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Typing :: Typed
Provides-Extra: docs
Provides-Extra: uvloop
Requires-Dist: furo (>=2023.9.10,<2024.0.0) ; extra == "docs"
Requires-Dist: sphinx (>=7.0.0,<8.0.0) ; extra == "docs"
Requires-Dist: sphinx-copybutton (>=0.5.0,<0.6.0) ; extra == "docs"
Requires-Dist: sphinx_autodoc_typehints (>=1.24.0,<2.0.0) ; extra == "docs"
Requires-Dist: uvloop (>=0.18,<0.19) ; extra == "uvloop"
Project-URL: Documentation, https://aiosmtplib.readthedocs.io/en/stable/
Project-URL: Repository, https://github.com/cole/aiosmtplib
Description-Content-Type: text/x-rst

aiosmtplib
==========

|circleci| |precommit.ci| |codecov| |pypi-version| |pypi-status| |downloads| |pypi-python-versions|
|pypi-license|

------------

aiosmtplib is an asynchronous SMTP client for use with asyncio.

For documentation, see `Read The Docs`_.

Quickstart
----------


..
  start quickstart

.. code-block:: python

    import asyncio
    from email.message import EmailMessage

    import aiosmtplib

    message = EmailMessage()
    message["From"] = "root@localhost"
    message["To"] = "<EMAIL>"
    message["Subject"] = "Hello World!"
    message.set_content("Sent via aiosmtplib")

    asyncio.run(aiosmtplib.send(message, hostname="127.0.0.1", port=25))

..
  end quickstart

Requirements
------------

..
  start requirements

Python 3.8+ is required.

..
  end requirements


Bug Reporting
-------------

..
  start bug-reporting

Bug reports (and feature requests) are welcome via `Github issues`_.

.. _Github issues: https://github.com/cole/aiosmtplib/issues

..
  end bug-reporting


.. |circleci| image:: https://circleci.com/gh/cole/aiosmtplib/tree/main.svg?style=shield
           :target: https://circleci.com/gh/cole/aiosmtplib/tree/main
           :alt: "aiosmtplib CircleCI build status"
.. |pypi-version| image:: https://img.shields.io/pypi/v/aiosmtplib.svg
                 :target: https://pypi.python.org/pypi/aiosmtplib
                 :alt: "aiosmtplib on the Python Package Index"
.. |pypi-python-versions| image:: https://img.shields.io/pypi/pyversions/aiosmtplib.svg
.. |pypi-status| image:: https://img.shields.io/pypi/status/aiosmtplib.svg
.. |pypi-license| image:: https://img.shields.io/pypi/l/aiosmtplib.svg
.. |codecov| image:: https://codecov.io/gh/cole/aiosmtplib/branch/main/graph/badge.svg
             :target: https://codecov.io/gh/cole/aiosmtplib
.. |downloads| image:: https://pepy.tech/badge/aiosmtplib
               :target: https://pepy.tech/project/aiosmtplib
               :alt: "aiosmtplib on pypy.tech"
.. |precommit.ci| image:: https://results.pre-commit.ci/badge/github/cole/aiosmtplib/main.svg
                  :target: https://results.pre-commit.ci/latest/github/cole/aiosmtplib/main
                  :alt: "pre-commit.ci status"
.. _Read The Docs: https://aiosmtplib.readthedocs.io/en/stable/

