# Sales Form Backend API

A Node.js backend service for handling sales form submissions with email functionality using Nodemailer.

## Features

- ✅ Express.js REST API
- ✅ Email sending with Nodemailer
- ✅ Form validation with Joi
- ✅ Rate limiting for security
- ✅ CORS support
- ✅ Environment-based configuration
- ✅ Error handling and logging
- ✅ Health check endpoints
- ✅ Confirmation emails to customers

## Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- SMTP email account (Gmail, Outlook, etc.)

## Installation

1. **Navigate to the backend directory:**
   ```bash
   cd backend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Configure environment variables:**
   ```bash
   cp .env.example .env
   ```
   
   Edit the `.env` file with your actual SMTP credentials:
   ```env
   # SMTP Email Configuration
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_SECURE=false
   SMTP_USER=<EMAIL>
   SMTP_PASS=your-app-password
   
   # Email Settings
   FROM_EMAIL=<EMAIL>
   FROM_NAME=Atandra Energy Sales Team
   TO_EMAIL=<EMAIL>
   ```

## Gmail Setup (Recommended)

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate an App Password:**
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a password for "Mail"
   - Use this password in `SMTP_PASS`

## Usage

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

The server will start on `http://localhost:5000`

## API Endpoints

### POST /api/email/send-enquiry
Sends a sales enquiry email.

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "company": "ABC Corp",
  "designation": "Manager",
  "city": "Chennai",
  "mobile": "+91 **********",
  "pincode": "600001",
  "products": "measure",
  "remarks": "Interested in power quality analyzers",
  "requestDemo": true,
  "requestCallback": false,
  "sendDetails": true,
  "sendUpdates": false
}
```

**Response:**
```json
{
  "success": true,
  "message": "Your enquiry has been submitted successfully. Our sales team will contact you soon.",
  "data": {
    "messageId": "<message-id>",
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

### GET /api/email/health
Checks email service health.

### GET /health
General server health check.

## Form Validation

The API validates all form fields:

- **name**: 2-100 characters, required
- **email**: Valid email format, required
- **company**: 2-200 characters, required
- **designation**: 2-100 characters, required
- **city**: 2-100 characters, required
- **mobile**: Valid phone number format, required
- **pincode**: 4-10 digits, required
- **products**: One of: measure, protect, conserve, consultation
- **remarks**: Max 1000 characters, optional
- **checkboxes**: Boolean values, optional

## Security Features

- **Rate Limiting**: 5 requests per 15 minutes per IP
- **CORS**: Configured for frontend domain
- **Helmet**: Security headers
- **Input Validation**: Joi schema validation
- **Error Handling**: Secure error responses

## Email Templates

The service sends two types of emails:

1. **Sales Team Notification**: Detailed enquiry information
2. **Customer Confirmation**: Thank you message with contact details

## Troubleshooting

### Common Issues

1. **SMTP Authentication Failed**
   - Verify SMTP credentials
   - Check if 2FA is enabled and app password is used
   - Ensure "Less secure app access" is enabled (if not using app password)

2. **Connection Timeout**
   - Check firewall settings
   - Verify SMTP host and port
   - Try different SMTP providers

3. **Rate Limiting**
   - Wait for the rate limit window to reset
   - Adjust `RATE_LIMIT_MAX_REQUESTS` in .env

### Logs

The server logs all important events:
- ✅ Successful operations
- ❌ Errors and failures
- ⚠️ Warnings

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | 5000 |
| `NODE_ENV` | Environment | development |
| `FRONTEND_URL` | Frontend URL for CORS | http://localhost:5173 |
| `SMTP_HOST` | SMTP server host | smtp.gmail.com |
| `SMTP_PORT` | SMTP server port | 587 |
| `SMTP_SECURE` | Use SSL/TLS | false |
| `SMTP_USER` | SMTP username | - |
| `SMTP_PASS` | SMTP password | - |
| `FROM_EMAIL` | Sender email | - |
| `FROM_NAME` | Sender name | Atandra Energy Sales Team |
| `TO_EMAIL` | Recipient email | <EMAIL> |

## Production Deployment

1. Set `NODE_ENV=production`
2. Use a process manager like PM2
3. Set up reverse proxy (nginx)
4. Use environment variables for sensitive data
5. Enable SSL/HTTPS
6. Monitor logs and performance

## Support

For issues or questions, contact the development team or check the logs for detailed error information.
