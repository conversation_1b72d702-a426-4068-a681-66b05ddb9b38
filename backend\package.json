{"name": "atandra-energy-backend", "version": "1.0.0", "description": "Backend API for Atandra Energy contact forms and email services", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "nodejs", "email", "nodemailer", "api"], "author": "Atandra Energy", "license": "ISC", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "morgan": "^1.10.0", "dotenv": "^16.3.1", "joi": "^17.11.0", "nodemailer": "^6.9.7"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}