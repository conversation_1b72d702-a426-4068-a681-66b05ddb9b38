const express = require('express');
const Joi = require('joi');
const emailService = require('../services/emailService');

const router = express.Router();

// Validation schema for form data
const salesFormSchema = Joi.object({
  name: Joi.string().min(2).max(100).required().messages({
    'string.empty': 'Name is required',
    'string.min': 'Name must be at least 2 characters long',
    'string.max': 'Name cannot exceed 100 characters'
  }),
  email: Joi.string().email().required().messages({
    'string.empty': 'Email is required',
    'string.email': 'Please provide a valid email address'
  }),
  company: Joi.string().min(2).max(200).required().messages({
    'string.empty': 'Company name is required',
    'string.min': 'Company name must be at least 2 characters long',
    'string.max': 'Company name cannot exceed 200 characters'
  }),
  designation: Joi.string().min(2).max(100).required().messages({
    'string.empty': 'Designation is required',
    'string.min': 'Designation must be at least 2 characters long',
    'string.max': 'Designation cannot exceed 100 characters'
  }),
  city: Joi.string().min(2).max(100).required().messages({
    'string.empty': 'City is required',
    'string.min': 'City must be at least 2 characters long',
    'string.max': 'City cannot exceed 100 characters'
  }),
  mobile: Joi.string().pattern(/^[+]?[\d\s\-\(\)]{10,15}$/).required().messages({
    'string.empty': 'Mobile number is required',
    'string.pattern.base': 'Please provide a valid mobile number'
  }),
  pincode: Joi.string().pattern(/^[\d]{4,10}$/).required().messages({
    'string.empty': 'Pincode is required',
    'string.pattern.base': 'Please provide a valid pincode'
  }),
  products: Joi.string().valid('measure', 'protect', 'conserve', 'consultation').required().messages({
    'string.empty': 'Please select a product category',
    'any.only': 'Please select a valid product category'
  }),
  remarks: Joi.string().max(1000).allow('').optional().messages({
    'string.max': 'Remarks cannot exceed 1000 characters'
  }),
  requestDemo: Joi.boolean().optional(),
  requestCallback: Joi.boolean().optional(),
  sendDetails: Joi.boolean().optional(),
  sendUpdates: Joi.boolean().optional()
});

// POST /api/email/send-enquiry
router.post('/send-enquiry', async (req, res) => {
  try {
    console.log('📧 Received form submission:', req.body);

    // Validate request body
    const { error, value } = salesFormSchema.validate(req.body, { abortEarly: false });
    
    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationErrors
      });
    }

    // Check if email service is ready (but don't fail if SMTP is down)
    const isEmailServiceReady = await emailService.verifyConnection();
    if (!isEmailServiceReady) {
      console.warn('⚠️ SMTP connection failed, but continuing with form processing...');
    }

    // Try to send enquiry email to sales team
    let emailResult = null;
    let emailSent = false;

    if (isEmailServiceReady) {
      try {
        emailResult = await emailService.sendSalesEnquiry(value);
        emailSent = true;
        console.log('✅ Sales enquiry email sent successfully');

        // Send confirmation email to customer (optional, don't fail if this fails)
        try {
          await emailService.sendConfirmationEmail(value);
          console.log('✅ Confirmation email sent to customer');
        } catch (confirmationError) {
          console.warn('⚠️ Failed to send confirmation email to customer:', confirmationError.message);
          // Don't fail the entire request if confirmation email fails
        }
      } catch (emailError) {
        console.error('❌ Failed to send sales enquiry email:', emailError.message);
        emailSent = false;
      }
    }

    // Log the form submission for manual processing if email failed
    if (!emailSent) {
      console.log('📝 FORM SUBMISSION (Email service unavailable):', JSON.stringify(value, null, 2));
      console.log('⚠️ Please manually process this enquiry as email service is currently unavailable.');
    }

    res.status(200).json({
      success: true,
      message: emailSent
        ? 'Your enquiry has been submitted successfully. Our sales team will contact you soon.'
        : 'Your enquiry has been received successfully. Our sales team will contact you soon. (Note: Email service is temporarily unavailable, but your request has been logged.)',
      data: {
        messageId: emailResult?.messageId || `manual-${Date.now()}`,
        timestamp: new Date().toISOString(),
        emailSent: emailSent
      }
    });

  } catch (error) {
    console.error('❌ Error processing form submission:', error);
    
    // Handle specific error types
    if (error.code === 'EAUTH') {
      return res.status(503).json({
        success: false,
        message: 'Email authentication failed. Please contact support.',
        error: 'EMAIL_AUTH_FAILED'
      });
    }
    
    if (error.code === 'ECONNECTION') {
      return res.status(503).json({
        success: false,
        message: 'Unable to connect to email server. Please try again later.',
        error: 'EMAIL_CONNECTION_FAILED'
      });
    }

    res.status(500).json({
      success: false,
      message: 'An unexpected error occurred. Please try again later.',
      error: process.env.NODE_ENV === 'development' ? error.message : 'INTERNAL_SERVER_ERROR'
    });
  }
});

// GET /api/email/health
router.get('/health', async (req, res) => {
  try {
    const isEmailServiceReady = await emailService.verifyConnection();
    
    res.status(200).json({
      success: true,
      emailService: {
        status: isEmailServiceReady ? 'connected' : 'disconnected',
        host: process.env.SMTP_HOST,
        port: process.env.SMTP_PORT,
        secure: process.env.SMTP_SECURE === 'true'
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(503).json({
      success: false,
      message: 'Email service health check failed',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

module.exports = router;
